"""
Contact Page - DevOps AI Team Platform
Investor-focused contact page with team information and engagement options
"""

import streamlit as st
import plotly.graph_objects as go
import pandas as pd
from datetime import datetime

def show_contact_page():
    """Display the contact page with investor focus"""
    
    # Page Header
    st.markdown("""
    <div class="fade-in">
        <div class="main-header">
            📞 Contact Us
        </div>
        <div class="sub-header">
            Connect with the Future of DevOps
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Quick Contact Options
    st.markdown("## 🚀 Get Started Today")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        <div class="contact-form" style="text-align: center;">
            <h3>📊 For Investors</h3>
            <p>Access our pitch deck, financial projections, and investment opportunities</p>
            <a href="#investor-form" class="cta-button">📈 Investor Portal</a>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="contact-form" style="text-align: center;">
            <h3>🏢 For Enterprises</h3>
            <p>Schedule a personalized demo and discuss enterprise solutions</p>
            <a href="#enterprise-form" class="cta-button">🎯 Book Demo</a>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("""
        <div class="contact-form" style="text-align: center;">
            <h3>🤝 For Partners</h3>
            <p>Explore partnership opportunities and integration possibilities</p>
            <a href="#partner-form" class="cta-button">🔗 Partner With Us</a>
        </div>
        """, unsafe_allow_html=True)
    
    # Contact Form Section
    st.markdown("---")
    st.markdown("## 📝 Contact Form")
    
    contact_type = st.selectbox(
        "I am interested in:",
        ["Investment Opportunities", "Enterprise Solutions", "Partnership", "General Inquiry"]
    )
    
    col1, col2 = st.columns(2)
    
    with col1:
        name = st.text_input("Full Name *")
        email = st.text_input("Email Address *")
        company = st.text_input("Company/Organization")
        title = st.text_input("Job Title")
    
    with col2:
        phone = st.text_input("Phone Number")
        company_size = st.selectbox("Company Size", 
                                   ["Startup (1-50)", "SME (51-500)", "Enterprise (500+)", "Investment Firm", "Other"])
        timeline = st.selectbox("Timeline", 
                               ["Immediate", "Within 1 month", "Within 3 months", "Within 6 months", "Just exploring"])
        budget = st.selectbox("Budget Range", 
                             ["< $10K/month", "$10K-$50K/month", "$50K-$100K/month", "> $100K/month", "To be discussed"])
    
    message = st.text_area("Message", placeholder="Tell us about your specific needs, questions, or investment interests...")
    
    if st.button("📤 Send Message", type="primary"):
        if name and email:
            st.success("✅ Thank you for your interest! We'll get back to you within 24 hours.")
            # In a real application, this would send the form data to a backend
        else:
            st.error("❌ Please fill in all required fields (marked with *)")
    
    # Team Section
    st.markdown("---")
    st.markdown("## 👥 Meet Our Team")
    
    # Founder/Leadership Team
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        <div class="contact-form">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="width: 100px; height: 100px; background: linear-gradient(135deg, #1E3A8A, #0F766E); border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: bold;">
                    JD
                </div>
            </div>
            <h4>John Doe</h4>
            <p><strong>CEO & Co-Founder</strong></p>
            <p>Former VP of Engineering at TechCorp. 15+ years in DevOps and cloud infrastructure. PhD in Computer Science from MIT.</p>
            <p>📧 <EMAIL></p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="contact-form">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="width: 100px; height: 100px; background: linear-gradient(135deg, #DC2626, #F59E0B); border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: bold;">
                    JS
                </div>
            </div>
            <h4>Jane Smith</h4>
            <p><strong>CTO & Co-Founder</strong></p>
            <p>Former Principal Engineer at CloudScale. Expert in AI/ML and distributed systems. MS in AI from Stanford.</p>
            <p>📧 <EMAIL></p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("""
        <div class="contact-form">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="width: 100px; height: 100px; background: linear-gradient(135deg, #7C3AED, #059669); border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: bold;">
                    MB
                </div>
            </div>
            <h4>Mike Brown</h4>
            <p><strong>VP of Sales</strong></p>
            <p>Former Enterprise Sales Director at InnovateLab. 12+ years in B2B SaaS sales. MBA from Wharton.</p>
            <p>📧 <EMAIL></p>
        </div>
        """, unsafe_allow_html=True)
    
    # Advisory Board
    st.markdown("### 🎯 Advisory Board")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **Dr. Sarah Wilson** - Former CTO at DataFlow  
        Expert in enterprise AI and cloud architecture
        
        **Robert Chen** - Managing Partner at TechVentures  
        20+ years in venture capital and technology investments
        """)
    
    with col2:
        st.markdown("""
        **Lisa Garcia** - Former VP of DevOps at GlobalTech  
        Industry expert in DevOps transformation and automation
        
        **David Kim** - Security Consultant  
        Former CISO at multiple Fortune 500 companies
        """)
    
    # Company Information
    st.markdown("---")
    st.markdown("## 🏢 Company Information")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 📍 Headquarters
        **DevOps AI Team Platform Inc.**  
        123 Innovation Drive  
        San Francisco, CA 94105  
        United States
        
        **Phone:** +****************  
        **Email:** <EMAIL>  
        **Website:** www.devopsai.com
        """)
    
    with col2:
        st.markdown("""
        ### 🌍 Global Presence
        **Development Center**  
        Austin, TX
        
        **European Office**  
        London, UK (Opening Q2 2024)
        
        **APAC Office**  
        Singapore (Planned 2025)
        """)
    
    # Investment Information
    st.markdown("---")
    st.markdown("## 💰 Investment Information")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 📊 Current Funding Round
        **Series A: $10M**
        - Lead Investor: TechVentures
        - Participating: InnovateCapital, CloudFund
        - Use of Funds: Product development, market expansion
        - Timeline: Closing Q1 2024
        """)
        
        st.markdown("""
        ### 📈 Key Metrics
        - **Revenue Growth:** 300% YoY
        - **Customer Retention:** 95%
        - **Market Size:** $45B TAM
        - **Team Size:** 25 employees
        """)
    
    with col2:
        st.markdown("""
        ### 🎯 Investment Highlights
        - First-to-market multi-agent DevOps platform
        - Proven enterprise traction with Fortune 500 clients
        - Strong technical team with deep domain expertise
        - Clear path to profitability and scale
        """)
        
        st.markdown("""
        ### 📋 Available Documents
        - Executive Summary
        - Pitch Deck (20 slides)
        - Financial Projections (5-year)
        - Technical Architecture Overview
        - Customer Case Studies
        """)
    
    # Download Section
    st.markdown("---")
    st.markdown("## 📥 Download Resources")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📊 Download Pitch Deck", use_container_width=True):
            st.info("📧 Pitch deck will be sent to your email after form submission")
    
    with col2:
        if st.button("📈 Financial Projections", use_container_width=True):
            st.info("📧 Financial data available for qualified investors")
    
    with col3:
        if st.button("🔧 Technical Overview", use_container_width=True):
            st.info("📧 Technical documentation available upon request")
    
    # Social Proof and Press
    st.markdown("---")
    st.markdown("## 📰 Press & Recognition")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 🏆 Awards & Recognition
        - **TechCrunch Disrupt Finalist** (2023)
        - **AI Innovation Award** - DevOps World (2023)
        - **Best Startup** - CloudExpo (2023)
        - **Rising Star** - VentureBeat (2023)
        """)
    
    with col2:
        st.markdown("""
        ### 📺 Media Coverage
        - **TechCrunch:** "The Future of DevOps is Multi-Agent AI"
        - **Forbes:** "How AI Agents are Revolutionizing Infrastructure"
        - **VentureBeat:** "DevOps AI Raises $10M Series A"
        - **The Information:** "Enterprise AI Adoption Accelerates"
        """)
    
    # Call to Action Footer
    st.markdown("""
    <div style="text-align: center; padding: 2rem; background: linear-gradient(135deg, #1E3A8A 0%, #0F766E 100%); border-radius: 15px; color: white; margin-top: 2rem;">
        <h2 style="margin-top: 0;">Ready to Join the DevOps Revolution?</h2>
        <p style="font-size: 1.1rem; margin-bottom: 2rem;">
            Whether you're an investor, enterprise customer, or potential partner, 
            we'd love to hear from you and explore how we can work together.
        </p>
        <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap;">
            <a href="mailto:<EMAIL>" class="cta-button" style="background-color: white; color: #1E3A8A;">📧 <EMAIL></a>
            <a href="tel:+15551234567" class="cta-button" style="background-color: #DC2626;">📞 +****************</a>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Footer with additional contact methods
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; padding: 1rem; color: #6B7280;">
        <p>Follow us on social media for the latest updates:</p>
        <p>
            <a href="#" style="color: #1E3A8A; text-decoration: none; margin: 0 1rem;">LinkedIn</a> |
            <a href="#" style="color: #1E3A8A; text-decoration: none; margin: 0 1rem;">Twitter</a> |
            <a href="#" style="color: #1E3A8A; text-decoration: none; margin: 0 1rem;">GitHub</a> |
            <a href="#" style="color: #1E3A8A; text-decoration: none; margin: 0 1rem;">YouTube</a>
        </p>
        <p style="font-size: 0.9rem; margin-top: 1rem;">
            © 2024 DevOps AI Team Platform Inc. All rights reserved. | 
            <a href="#" style="color: #1E3A8A;">Privacy Policy</a> | 
            <a href="#" style="color: #1E3A8A;">Terms of Service</a>
        </p>
    </div>
    """, unsafe_allow_html=True)
