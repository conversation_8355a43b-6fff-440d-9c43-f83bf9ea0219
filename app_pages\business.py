"""
Business Value Page - DevOps AI Team Platform
Interactive ROI calculator and business value proposition
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np

def show_business_page():
    """Display the business value page with ROI calculator"""
    
    # Page Header
    st.markdown("""
    <div class="fade-in">
        <div class="main-header">
            💰 Business Value
        </div>
        <div class="sub-header">
            Quantifiable ROI and Market Opportunity
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # ROI Calculator Section
    st.markdown("## 🧮 Interactive ROI Calculator")
    st.markdown("Customize the parameters below to see your potential return on investment:")
    
    # Input parameters
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 📊 Current State")
        current_devops_team_size = st.slider("DevOps Team Size", 1, 50, 8)
        avg_salary = st.slider("Average DevOps Salary ($K)", 80, 200, 120)
        deployment_frequency = st.selectbox("Deployment Frequency", 
                                          ["Daily", "Weekly", "Bi-weekly", "Monthly"])
        current_deployment_time = st.slider("Average Deployment Time (hours)", 1, 24, 8)
        incident_rate = st.slider("Monthly Incidents", 0, 20, 5)
    
    with col2:
        st.markdown("### 🚀 With Our Platform")
        platform_cost = st.slider("Platform Cost ($/month)", 5000, 50000, 15000)
        time_reduction = st.slider("Time Reduction (%)", 50, 90, 75)
        quality_improvement = st.slider("Quality Improvement (%)", 20, 95, 85)
        incident_reduction = st.slider("Incident Reduction (%)", 30, 95, 80)
    
    # Calculate ROI
    roi_results = calculate_roi(
        current_devops_team_size, avg_salary, deployment_frequency,
        current_deployment_time, incident_rate, platform_cost,
        time_reduction, quality_improvement, incident_reduction
    )
    
    # Display ROI Results
    st.markdown("---")
    st.markdown("### 📈 Your ROI Analysis")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "Annual Savings",
            f"${roi_results['annual_savings']:,.0f}",
            f"{roi_results['savings_percentage']:.1f}% reduction"
        )
    
    with col2:
        st.metric(
            "ROI",
            f"{roi_results['roi']:.0f}%",
            "Return on Investment"
        )
    
    with col3:
        st.metric(
            "Payback Period",
            f"{roi_results['payback_months']:.1f} months",
            "Time to break even"
        )
    
    with col4:
        st.metric(
            "3-Year Value",
            f"${roi_results['three_year_value']:,.0f}",
            "Total value creation"
        )
    
    # ROI Breakdown Chart
    create_roi_chart(roi_results)
    
    # Market Analysis
    st.markdown("---")
    st.markdown("## 📊 Market Analysis")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 🎯 Target Market Size
        
        **Total Addressable Market (TAM):** $45B
        - Global DevOps tools market
        - Growing at 18.6% CAGR
        - Expected to reach $57B by 2025
        
        **Serviceable Addressable Market (SAM):** $12B
        - Enterprise DevOps automation
        - AI-powered infrastructure tools
        - Multi-cloud management platforms
        
        **Serviceable Obtainable Market (SOM):** $500M
        - Large enterprises (1000+ employees)
        - Complex infrastructure requirements
        - High DevOps maturity organizations
        """)
    
    with col2:
        # Market size visualization
        market_data = pd.DataFrame({
            'Market': ['TAM', 'SAM', 'SOM'],
            'Size ($B)': [45, 12, 0.5],
            'Color': ['#1E3A8A', '#0F766E', '#DC2626']
        })
        
        fig = px.funnel(market_data, x='Size ($B)', y='Market', color='Market',
                       color_discrete_map={'TAM': '#1E3A8A', 'SAM': '#0F766E', 'SOM': '#DC2626'})
        fig.update_layout(title="Market Opportunity Funnel", height=400)
        st.plotly_chart(fig, use_container_width=True)
    
    # Competitive Landscape
    st.markdown("---")
    st.markdown("## 🏆 Competitive Landscape")
    
    # Competitive positioning chart
    competitors_data = pd.DataFrame({
        'Company': ['Traditional Tools', 'Jenkins/GitLab', 'Single LLM Solutions', 'Our Platform'],
        'Innovation': [30, 50, 70, 95],
        'Market Share': [40, 35, 15, 5],
        'Quality Score': [60, 70, 75, 98],
        'Size': [1000, 800, 400, 200]
    })
    
    fig = px.scatter(competitors_data, x='Innovation', y='Quality Score', 
                    size='Size', color='Company', hover_name='Company',
                    title="Competitive Positioning: Innovation vs Quality",
                    labels={'Innovation': 'Innovation Score', 'Quality Score': 'Quality Score'})
    
    fig.update_layout(height=500)
    st.plotly_chart(fig, use_container_width=True)
    
    # Unique Selling Propositions
    st.markdown("### 🎯 Unique Selling Propositions")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        <div class="metric-card">
            <h4>🧠 Multi-Agent Intelligence</h4>
            <p>First platform to deploy specialized AI agents that collaborate like a real DevOps team</p>
            <ul>
                <li>Domain expertise per agent</li>
                <li>Collaborative decision making</li>
                <li>Quality validation workflows</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="metric-card">
            <h4>⚡ Enterprise-Grade Performance</h4>
            <p>Unmatched quality and reliability for mission-critical infrastructure</p>
            <ul>
                <li>99.9% uptime guarantee</li>
                <li>85% faster deployments</li>
                <li>60% cost reduction</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("""
        <div class="metric-card">
            <h4>🔧 Seamless Integration</h4>
            <p>Works with existing tools and workflows without disruption</p>
            <ul>
                <li>API-first architecture</li>
                <li>Tool chain compatibility</li>
                <li>Gradual adoption path</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
    
    # Monetization Strategy
    st.markdown("---")
    st.markdown("## 💼 Monetization Strategy")
    
    # Pricing tiers
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        <div class="metric-card" style="border-top: 3px solid #0F766E;">
            <h4>🚀 Starter</h4>
            <h3 style="color: #0F766E;">$5,000/month</h3>
            <ul>
                <li>Up to 5 agents</li>
                <li>Basic workflows</li>
                <li>Standard support</li>
                <li>API access</li>
            </ul>
            <p><strong>Target:</strong> Small-medium enterprises</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="metric-card" style="border-top: 3px solid #1E3A8A;">
            <h4>🏢 Enterprise</h4>
            <h3 style="color: #1E3A8A;">$15,000/month</h3>
            <ul>
                <li>Unlimited agents</li>
                <li>Advanced workflows</li>
                <li>Priority support</li>
                <li>Custom integrations</li>
            </ul>
            <p><strong>Target:</strong> Large enterprises</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("""
        <div class="metric-card" style="border-top: 3px solid #DC2626;">
            <h4>🎯 Custom</h4>
            <h3 style="color: #DC2626;">Contact Us</h3>
            <ul>
                <li>Dedicated deployment</li>
                <li>Custom agent development</li>
                <li>24/7 dedicated support</li>
                <li>On-premise options</li>
            </ul>
            <p><strong>Target:</strong> Fortune 500</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Revenue Projections
    st.markdown("### 📈 Revenue Projections")
    
    # Create revenue projection chart
    years = list(range(2024, 2029))
    revenue_data = pd.DataFrame({
        'Year': years,
        'Starter Tier': [2, 8, 20, 35, 50],  # Millions
        'Enterprise Tier': [5, 25, 60, 120, 200],
        'Custom Tier': [1, 5, 15, 35, 75]
    })
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(x=revenue_data['Year'], y=revenue_data['Starter Tier'],
                            mode='lines+markers', name='Starter Tier', fill='tonexty'))
    fig.add_trace(go.Scatter(x=revenue_data['Year'], y=revenue_data['Enterprise Tier'],
                            mode='lines+markers', name='Enterprise Tier', fill='tonexty'))
    fig.add_trace(go.Scatter(x=revenue_data['Year'], y=revenue_data['Custom Tier'],
                            mode='lines+markers', name='Custom Tier', fill='tonexty'))
    
    fig.update_layout(
        title="5-Year Revenue Projection ($M)",
        xaxis_title="Year",
        yaxis_title="Revenue ($M)",
        height=400
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # Investment Requirements
    st.markdown("---")
    st.markdown("## 💰 Investment Requirements")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 🎯 Funding Rounds
        
        **Seed Round: $2M** (Completed)
        - Product development
        - Initial team building
        - MVP validation
        
        **Series A: $10M** (Current)
        - Market expansion
        - Sales team scaling
        - Enterprise features
        
        **Series B: $25M** (Planned 2025)
        - International expansion
        - Advanced AI capabilities
        - Strategic partnerships
        """)
    
    with col2:
        st.markdown("""
        ### 📊 Use of Funds (Series A)
        
        **Engineering (40%): $4M**
        - Agent development
        - Platform scaling
        - Security enhancements
        
        **Sales & Marketing (35%): $3.5M**
        - Enterprise sales team
        - Marketing campaigns
        - Customer success
        
        **Operations (25%): $2.5M**
        - Infrastructure costs
        - Legal and compliance
        - Working capital
        """)
    
    # Call to Action
    st.markdown("""
    <div style="text-align: center; padding: 2rem; background: linear-gradient(135deg, #1E3A8A 0%, #0F766E 100%); border-radius: 15px; color: white; margin-top: 2rem;">
        <h2 style="margin-top: 0;">Ready to Invest in the Future of DevOps?</h2>
        <p style="font-size: 1.1rem; margin-bottom: 2rem;">
            Join us in revolutionizing enterprise infrastructure automation with multi-agent AI technology.
        </p>
        <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap;">
            <a href="#contact" class="cta-button" style="background-color: white; color: #1E3A8A;">📊 Download Pitch Deck</a>
            <a href="#demo" class="cta-button" style="background-color: #DC2626;">🎯 Schedule Demo</a>
        </div>
    </div>
    """, unsafe_allow_html=True)

def calculate_roi(team_size, avg_salary, deployment_freq, deployment_time, 
                 incident_rate, platform_cost, time_reduction, quality_improvement, incident_reduction):
    """Calculate ROI based on input parameters"""
    
    # Convert deployment frequency to monthly deployments
    freq_mapping = {"Daily": 30, "Weekly": 4, "Bi-weekly": 2, "Monthly": 1}
    monthly_deployments = freq_mapping[deployment_freq]
    
    # Current costs
    annual_team_cost = team_size * avg_salary * 1000
    deployment_cost_per_hour = (annual_team_cost / 2080)  # Assuming 2080 work hours per year
    monthly_deployment_cost = monthly_deployments * deployment_time * deployment_cost_per_hour
    annual_deployment_cost = monthly_deployment_cost * 12
    
    # Incident costs (assuming $10K per incident)
    annual_incident_cost = incident_rate * 12 * 10000
    
    total_current_cost = annual_team_cost + annual_deployment_cost + annual_incident_cost
    
    # With platform
    annual_platform_cost = platform_cost * 12
    reduced_deployment_time = deployment_time * (1 - time_reduction/100)
    reduced_monthly_deployment_cost = monthly_deployments * reduced_deployment_time * deployment_cost_per_hour
    reduced_annual_deployment_cost = reduced_monthly_deployment_cost * 12
    
    reduced_incidents = incident_rate * (1 - incident_reduction/100)
    reduced_annual_incident_cost = reduced_incidents * 12 * 10000
    
    # Assume 30% team efficiency improvement
    reduced_team_cost = annual_team_cost * 0.7
    
    total_new_cost = annual_platform_cost + reduced_team_cost + reduced_annual_deployment_cost + reduced_annual_incident_cost
    
    # Calculate savings and ROI
    annual_savings = total_current_cost - total_new_cost
    roi = (annual_savings / annual_platform_cost) * 100
    payback_months = annual_platform_cost / (annual_savings / 12)
    three_year_value = annual_savings * 3 - annual_platform_cost * 3
    savings_percentage = (annual_savings / total_current_cost) * 100
    
    return {
        'annual_savings': annual_savings,
        'roi': roi,
        'payback_months': payback_months,
        'three_year_value': three_year_value,
        'savings_percentage': savings_percentage,
        'current_cost': total_current_cost,
        'new_cost': total_new_cost
    }

def create_roi_chart(roi_results):
    """Create ROI breakdown chart"""
    
    # Cost breakdown data
    categories = ['Current Annual Cost', 'New Annual Cost', 'Annual Savings']
    values = [roi_results['current_cost'], roi_results['new_cost'], roi_results['annual_savings']]
    colors = ['#DC2626', '#F59E0B', '#0F766E']
    
    fig = go.Figure(data=[
        go.Bar(x=categories, y=values, marker_color=colors)
    ])
    
    fig.update_layout(
        title="Annual Cost Comparison",
        yaxis_title="Cost ($)",
        height=400
    )
    
    # Format y-axis as currency
    fig.update_layout(yaxis_tickformat='$,.0f')
    
    st.plotly_chart(fig, use_container_width=True)
