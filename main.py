"""
DevOps AI Team Platform - Main Application
A professional Streamlit web application showcasing an innovative AI-powered DevOps platform
where multiple specialized LLM agents collaborate in structured teams.
"""

import streamlit as st

# Page configuration
st.set_page_config(
    page_title="DevOps AI Team Platform",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Ensure our navigation is visible and styled properly
st.markdown("""
<style>
    /* Style our navigation buttons */
    .stButton > button {
        width: 100%;
        margin: 2px 0;
        border-radius: 8px;
    }

    /* Ensure sidebar content is visible */
    section[data-testid="stSidebar"] {
        display: block !important;
    }

    /* Style the sidebar */
    .css-1d391kg {
        padding-top: 1rem;
    }
</style>
""", unsafe_allow_html=True)

# Color scheme and styling constants
PRIMARY_COLOR = "#1E3A8A"  # Professional blue
SECONDARY_COLOR = "#0F766E"  # Teal for tech appeal
ACCENT_COLOR = "#DC2626"  # Red for alerts/important elements
BACKGROUND_COLOR = "#F8FAFC"  # Clean light background

def load_css():
    """Load custom CSS for professional styling"""
    st.markdown(f"""
    <style>
    .main {{
        background-color: {BACKGROUND_COLOR};
    }}
    
    .main-header {{
        font-size: 3rem;
        color: {PRIMARY_COLOR};
        text-align: center;
        margin-bottom: 2rem;
        font-weight: 700;
    }}
    
    .sub-header {{
        font-size: 1.5rem;
        color: {SECONDARY_COLOR};
        text-align: center;
        margin-bottom: 1.5rem;
        font-weight: 500;
    }}
    
    .metric-card {{
        background-color: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
        border-left: 4px solid {PRIMARY_COLOR};
    }}
    
    .value-prop {{
        background: linear-gradient(135deg, {PRIMARY_COLOR} 0%, {SECONDARY_COLOR} 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin: 2rem 0;
        text-align: center;
    }}
    
    .agent-card {{
        background-color: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
        border-top: 3px solid {SECONDARY_COLOR};
    }}
    
    .cta-button {{
        background-color: {ACCENT_COLOR};
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        display: inline-block;
        margin: 0.5rem;
        transition: all 0.3s ease;
    }}
    
    .cta-button:hover {{
        background-color: #B91C1C;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }}
    
    .sidebar .sidebar-content {{
        background-color: white;
    }}
    
    .stSelectbox > div > div {{
        background-color: white;
    }}
    
    .workflow-step {{
        background-color: white;
        padding: 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
        border-left: 3px solid {PRIMARY_COLOR};
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }}
    
    .demo-container {{
        background-color: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
    }}
    
    .contact-form {{
        background-color: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
    }}
    
    /* Hide Streamlit branding */
    #MainMenu {{visibility: hidden;}}
    footer {{visibility: hidden;}}
    header {{visibility: hidden;}}
    
    /* Custom animations */
    @keyframes fadeIn {{
        from {{ opacity: 0; transform: translateY(20px); }}
        to {{ opacity: 1; transform: translateY(0); }}
    }}
    
    .fade-in {{
        animation: fadeIn 0.6s ease-out;
    }}
    </style>
    """, unsafe_allow_html=True)

def create_navigation():
    """Create navigation sidebar"""
    with st.sidebar:
        st.markdown("## 🚀 DevOps AI")
        st.markdown("Multi-Agent Platform")
        st.markdown("---")
        st.markdown("### 📍 Navigation")

        # Initialize current page if not set
        if 'current_page' not in st.session_state:
            st.session_state.current_page = 'home'

        # Create navigation buttons
        if st.button("🏠 Home", key="nav_home", use_container_width=True,
                    type="primary" if st.session_state.current_page == "home" else "secondary"):
            st.session_state.current_page = "home"
            st.rerun()

        if st.button("� The Concept", key="nav_concept", use_container_width=True,
                    type="primary" if st.session_state.current_page == "concept" else "secondary"):
            st.session_state.current_page = "concept"
            st.rerun()

        if st.button("🛠️ How It Works", key="nav_workflow", use_container_width=True,
                    type="primary" if st.session_state.current_page == "workflow" else "secondary"):
            st.session_state.current_page = "workflow"
            st.rerun()

        if st.button("💰 Business Value", key="nav_business", use_container_width=True,
                    type="primary" if st.session_state.current_page == "business" else "secondary"):
            st.session_state.current_page = "business"
            st.rerun()

        if st.button("📈 Demo", key="nav_demo", use_container_width=True,
                    type="primary" if st.session_state.current_page == "demo" else "secondary"):
            st.session_state.current_page = "demo"
            st.rerun()

        if st.button("📞 Contact", key="nav_contact", use_container_width=True,
                    type="primary" if st.session_state.current_page == "contact" else "secondary"):
            st.session_state.current_page = "contact"
            st.rerun()

        return st.session_state.current_page

def main():
    """Main application entry point"""
    load_css()

    # Navigation
    current_page = create_navigation()

    # Route to appropriate page
    if current_page == 'home':
        from app_pages.home import show_home_page
        show_home_page()
    elif current_page == 'concept':
        from app_pages.concept import show_concept_page
        show_concept_page()
    elif current_page == 'workflow':
        from app_pages.workflow import show_workflow_page
        show_workflow_page()
    elif current_page == 'business':
        from app_pages.business import show_business_page
        show_business_page()
    elif current_page == 'demo':
        from app_pages.demo import show_demo_page
        show_demo_page()
    elif current_page == 'contact':
        from app_pages.contact import show_contact_page
        show_contact_page()

if __name__ == "__main__":
    main()
