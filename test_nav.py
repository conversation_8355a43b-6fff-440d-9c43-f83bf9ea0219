import streamlit as st

st.set_page_config(page_title="Test Navigation", layout="wide")

def test_navigation():
    with st.sidebar:
        st.markdown("## 🚀 DevOps AI")
        st.markdown("### Navigation Test")
        
        if st.button("🏠 Home", key="home", use_container_width=True):
            st.session_state.page = "home"
        if st.button("🚀 Concept", key="concept", use_container_width=True):
            st.session_state.page = "concept"
        if st.button("💰 Business", key="business", use_container_width=True):
            st.session_state.page = "business"
    
    # Initialize page
    if 'page' not in st.session_state:
        st.session_state.page = 'home'
    
    return st.session_state.page

def main():
    current_page = test_navigation()
    
    st.title(f"Current Page: {current_page}")
    
    if current_page == 'home':
        st.write("This is the HOME page")
    elif current_page == 'concept':
        st.write("This is the CONCEPT page")
    elif current_page == 'business':
        st.write("This is the BUSINESS page")

if __name__ == "__main__":
    main()
