"""
Home Page - DevOps AI Team Platform
Professional landing page with hero section and value propositions
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
from datetime import datetime

def show_home_page():
    """Display the home page with hero section and value propositions"""
    
    # Hero Section
    st.markdown("""
    <div class="fade-in">
        <div class="main-header">
            🚀 DevOps AI Team Platform
        </div>
        <div class="sub-header">
            Revolutionary Multi-Agent LLM Architecture for Enterprise DevOps
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Value Proposition Section
    st.markdown("""
    <div class="value-prop fade-in">
        <h2 style="margin-top: 0;">Transform Your DevOps with Intelligent Agent Teams</h2>
        <p style="font-size: 1.2rem; margin-bottom: 2rem;">
            Our platform deploys specialized AI agents that collaborate like a world-class DevOps team, 
            delivering enterprise-grade infrastructure automation with unprecedented quality and speed.
        </p>
        <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap;">
            <a href="#demo" class="cta-button">🎯 See Live Demo</a>
            <a href="#business" class="cta-button">💰 Calculate ROI</a>
            <a href="#contact" class="cta-button">📞 Schedule Meeting</a>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Key Metrics Section
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown("""
        <div class="metric-card">
            <h3 style="color: #1E3A8A; margin-top: 0;">85%</h3>
            <p>Faster Deployment</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="metric-card">
            <h3 style="color: #1E3A8A; margin-top: 0;">99.9%</h3>
            <p>Code Quality Score</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("""
        <div class="metric-card">
            <h3 style="color: #1E3A8A; margin-top: 0;">60%</h3>
            <p>Cost Reduction</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        st.markdown("""
        <div class="metric-card">
            <h3 style="color: #1E3A8A; margin-top: 0;">24/7</h3>
            <p>Autonomous Operation</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Key Value Propositions
    st.markdown("## 🎯 Why Choose Our Multi-Agent Approach?")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 🧠 Specialized Intelligence
        - **Infrastructure Architect Agent**: Designs scalable, secure architectures
        - **Security Compliance Agent**: Ensures enterprise-grade security standards
        - **Deployment Orchestrator**: Manages complex deployment pipelines
        - **Quality Assurance Agent**: Validates every component before deployment
        """)
        
        st.markdown("""
        ### ⚡ Unprecedented Speed
        - Parallel processing across specialized agents
        - Automated quality gates and validation
        - Instant rollback and recovery mechanisms
        - Real-time monitoring and optimization
        """)
    
    with col2:
        st.markdown("""
        ### 🛡️ Enterprise-Grade Quality
        - Multi-layer validation and testing
        - Compliance with industry standards
        - Comprehensive audit trails
        - Zero-downtime deployment strategies
        """)
        
        st.markdown("""
        ### 💰 Measurable ROI
        - 85% reduction in deployment time
        - 60% lower operational costs
        - 99.9% uptime guarantee
        - Scalable pricing models
        """)
    
    # Trust Indicators
    st.markdown("---")
    st.markdown("## 🤝 Trusted by Industry Leaders")
    
    # Create mock client logos/names
    trust_col1, trust_col2, trust_col3, trust_col4 = st.columns(4)
    
    with trust_col1:
        st.markdown("""
        <div style="text-align: center; padding: 1rem; background: white; border-radius: 10px; margin: 0.5rem;">
            <h4 style="color: #1E3A8A;">TechCorp</h4>
            <p style="font-size: 0.9rem; color: #6B7280;">Fortune 500</p>
        </div>
        """, unsafe_allow_html=True)
    
    with trust_col2:
        st.markdown("""
        <div style="text-align: center; padding: 1rem; background: white; border-radius: 10px; margin: 0.5rem;">
            <h4 style="color: #1E3A8A;">CloudScale</h4>
            <p style="font-size: 0.9rem; color: #6B7280;">Unicorn Startup</p>
        </div>
        """, unsafe_allow_html=True)
    
    with trust_col3:
        st.markdown("""
        <div style="text-align: center; padding: 1rem; background: white; border-radius: 10px; margin: 0.5rem;">
            <h4 style="color: #1E3A8A;">DataFlow</h4>
            <p style="font-size: 0.9rem; color: #6B7280;">Enterprise</p>
        </div>
        """, unsafe_allow_html=True)
    
    with trust_col4:
        st.markdown("""
        <div style="text-align: center; padding: 1rem; background: white; border-radius: 10px; margin: 0.5rem;">
            <h4 style="color: #1E3A8A;">InnovateLab</h4>
            <p style="font-size: 0.9rem; color: #6B7280;">Research Institute</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Performance Comparison Chart
    st.markdown("## 📊 Performance Comparison")
    
    # Create comparison data
    comparison_data = pd.DataFrame({
        'Metric': ['Deployment Speed', 'Code Quality', 'Security Score', 'Maintenance Cost'],
        'Traditional DevOps': [30, 70, 65, 100],
        'Single LLM': [60, 75, 70, 80],
        'Our Multi-Agent Platform': [95, 99, 98, 40]
    })
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatterpolar(
        r=comparison_data['Traditional DevOps'],
        theta=comparison_data['Metric'],
        fill='toself',
        name='Traditional DevOps',
        line_color='#DC2626'
    ))
    
    fig.add_trace(go.Scatterpolar(
        r=comparison_data['Single LLM'],
        theta=comparison_data['Metric'],
        fill='toself',
        name='Single LLM',
        line_color='#F59E0B'
    ))
    
    fig.add_trace(go.Scatterpolar(
        r=comparison_data['Our Multi-Agent Platform'],
        theta=comparison_data['Metric'],
        fill='toself',
        name='Our Multi-Agent Platform',
        line_color='#0F766E'
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100]
            )),
        showlegend=True,
        title="Performance Comparison Across Key Metrics",
        height=500
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # Call to Action Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; padding: 2rem; background: linear-gradient(135deg, #1E3A8A 0%, #0F766E 100%); border-radius: 15px; color: white;">
        <h2 style="margin-top: 0;">Ready to Transform Your DevOps?</h2>
        <p style="font-size: 1.1rem; margin-bottom: 2rem;">
            Join the revolution in intelligent infrastructure automation. 
            Schedule a personalized demo and see how our multi-agent platform can accelerate your business.
        </p>
        <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap;">
            <a href="#demo" class="cta-button" style="background-color: white; color: #1E3A8A;">🚀 Start Free Trial</a>
            <a href="#contact" class="cta-button" style="background-color: #DC2626;">📅 Book Demo</a>
        </div>
    </div>
    """, unsafe_allow_html=True)
