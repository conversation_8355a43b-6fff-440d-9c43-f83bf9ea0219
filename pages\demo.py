"""
Demo Page - DevOps AI Team Platform
Interactive demonstration with real-time agent collaboration simulation
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import time
import random
import json

def show_demo_page():
    """Display the demo page with interactive scenarios"""
    
    # Page Header
    st.markdown("""
    <div class="fade-in">
        <div class="main-header">
            📈 Live Demo
        </div>
        <div class="sub-header">
            Experience Multi-Agent DevOps in Action
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Demo Introduction
    st.markdown("""
    ## 🎯 Interactive Demo Experience
    
    Choose from real-world DevOps scenarios below to see our multi-agent platform in action. 
    Watch as specialized AI agents collaborate to deliver enterprise-grade solutions in minutes, not hours.
    """)
    
    # Demo Scenario Selection
    demo_scenario = st.selectbox(
        "🎬 Select a Demo Scenario:",
        [
            "🚀 E-commerce Platform Deployment",
            "🔧 Microservices Architecture Setup", 
            "🛡️ Security Compliance Implementation",
            "📊 Real-time Analytics Pipeline",
            "🌐 Multi-Cloud Infrastructure Setup"
        ]
    )
    
    # Demo Controls
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        if st.button("▶️ Start Demo", type="primary", use_container_width=True):
            st.session_state.demo_running = True
            st.session_state.demo_scenario = demo_scenario
    
    with col2:
        if st.button("⏸️ Pause", use_container_width=True):
            st.session_state.demo_running = False
    
    with col3:
        if st.button("🔄 Reset", use_container_width=True):
            st.session_state.demo_running = False
            st.session_state.demo_step = 0
    
    # Initialize session state
    if 'demo_running' not in st.session_state:
        st.session_state.demo_running = False
    if 'demo_step' not in st.session_state:
        st.session_state.demo_step = 0
    
    # Demo Execution
    if st.session_state.demo_running:
        run_demo_scenario(demo_scenario)
    else:
        show_demo_preview(demo_scenario)
    
    # Comparison Section
    st.markdown("---")
    st.markdown("## 📊 Performance Comparison")
    
    show_performance_comparison()
    
    # Live Code Generation Demo
    st.markdown("---")
    st.markdown("## 💻 Live Code Generation")
    
    show_code_generation_demo()
    
    # Quality Metrics Dashboard
    st.markdown("---")
    st.markdown("## 📈 Real-Time Quality Metrics")
    
    show_quality_dashboard()

def show_demo_preview(scenario):
    """Show preview of the selected demo scenario"""
    
    scenario_details = {
        "🚀 E-commerce Platform Deployment": {
            "description": "Deploy a complete e-commerce platform with microservices architecture, payment processing, and real-time inventory management.",
            "components": ["Frontend (React)", "API Gateway", "User Service", "Product Service", "Payment Service", "Database Cluster", "Redis Cache", "Load Balancer"],
            "estimated_time": "12 minutes",
            "complexity": "High"
        },
        "🔧 Microservices Architecture Setup": {
            "description": "Set up a scalable microservices architecture with service mesh, monitoring, and automated deployment pipelines.",
            "components": ["Service Mesh (Istio)", "Container Registry", "CI/CD Pipeline", "Monitoring Stack", "Logging System", "API Documentation"],
            "estimated_time": "8 minutes", 
            "complexity": "Medium"
        },
        "🛡️ Security Compliance Implementation": {
            "description": "Implement comprehensive security measures including encryption, access controls, and compliance monitoring.",
            "components": ["IAM Policies", "Encryption Keys", "Security Groups", "Audit Logging", "Compliance Dashboard", "Vulnerability Scanner"],
            "estimated_time": "6 minutes",
            "complexity": "Medium"
        },
        "📊 Real-time Analytics Pipeline": {
            "description": "Build a real-time data analytics pipeline with stream processing, data warehousing, and visualization dashboards.",
            "components": ["Data Ingestion", "Stream Processing", "Data Warehouse", "Analytics Engine", "Visualization Dashboard", "Alert System"],
            "estimated_time": "10 minutes",
            "complexity": "High"
        },
        "🌐 Multi-Cloud Infrastructure Setup": {
            "description": "Deploy infrastructure across multiple cloud providers with unified management and disaster recovery.",
            "components": ["AWS Resources", "Azure Resources", "GCP Resources", "Cross-Cloud Networking", "Unified Monitoring", "Disaster Recovery"],
            "estimated_time": "15 minutes",
            "complexity": "Very High"
        }
    }
    
    details = scenario_details[scenario]
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown(f"""
        <div class="demo-container">
            <h3>{scenario}</h3>
            <p>{details['description']}</p>
            <p><strong>Estimated Time:</strong> {details['estimated_time']}</p>
            <p><strong>Complexity:</strong> {details['complexity']}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("### 🧩 Components to Deploy")
        for component in details['components']:
            st.markdown(f"- {component}")

def run_demo_scenario(scenario):
    """Run the selected demo scenario with real-time updates"""
    
    st.markdown("### 🎬 Demo in Progress...")
    
    # Create containers for different sections
    agent_container = st.container()
    progress_container = st.container()
    output_container = st.container()
    
    with agent_container:
        st.markdown("#### 🤖 Agent Activity")
        
        # Agent status display
        agents = {
            "Infrastructure Architect": {"status": "Idle", "progress": 0, "color": "#1E3A8A"},
            "Security Compliance": {"status": "Idle", "progress": 0, "color": "#DC2626"},
            "Deployment Orchestrator": {"status": "Idle", "progress": 0, "color": "#0F766E"},
            "Quality Assurance": {"status": "Idle", "progress": 0, "color": "#F59E0B"},
            "Monitoring & Alerting": {"status": "Idle", "progress": 0, "color": "#7C3AED"}
        }
        
        # Create agent status cards
        cols = st.columns(len(agents))
        agent_cards = {}
        
        for i, (agent, info) in enumerate(agents.items()):
            with cols[i]:
                agent_cards[agent] = st.empty()
                update_agent_card(agent_cards[agent], agent, info)
    
    with progress_container:
        st.markdown("#### 📊 Overall Progress")
        overall_progress = st.progress(0)
        progress_text = st.empty()
    
    with output_container:
        st.markdown("#### 📝 Generated Output")
        output_tabs = st.tabs(["Infrastructure Code", "Security Policies", "Deployment Scripts", "Monitoring Config"])
        
        with output_tabs[0]:
            infra_output = st.empty()
        with output_tabs[1]:
            security_output = st.empty()
        with output_tabs[2]:
            deployment_output = st.empty()
        with output_tabs[3]:
            monitoring_output = st.empty()
    
    # Simulate demo execution
    demo_steps = generate_demo_steps(scenario)
    
    for step_num, step in enumerate(demo_steps):
        if not st.session_state.get('demo_running', False):
            break
            
        # Update agent status
        agent = step['agent']
        agents[agent]['status'] = step['status']
        agents[agent]['progress'] = step['progress']
        
        # Update agent card
        update_agent_card(agent_cards[agent], agent, agents[agent])
        
        # Update overall progress
        overall_progress.progress((step_num + 1) / len(demo_steps))
        progress_text.text(f"Step {step_num + 1}/{len(demo_steps)}: {step['description']}")
        
        # Update output based on agent
        if agent == "Infrastructure Architect":
            infra_output.code(step.get('output', ''), language='yaml')
        elif agent == "Security Compliance":
            security_output.code(step.get('output', ''), language='json')
        elif agent == "Deployment Orchestrator":
            deployment_output.code(step.get('output', ''), language='bash')
        elif agent == "Monitoring & Alerting":
            monitoring_output.code(step.get('output', ''), language='yaml')
        
        time.sleep(1)  # Simulate processing time
    
    if st.session_state.get('demo_running', False):
        st.success("🎉 Demo completed successfully! All components deployed and validated.")
        
        # Show final metrics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Deployment Time", "8.5 min", "-75% vs traditional")
        with col2:
            st.metric("Quality Score", "99.2%", "+29% vs single LLM")
        with col3:
            st.metric("Security Score", "98.7%", "+33% vs manual")
        with col4:
            st.metric("Components", "12/12", "100% success rate")

def update_agent_card(container, agent_name, agent_info):
    """Update an agent status card"""
    
    status_color = "#10B981" if agent_info['status'] == "Complete" else "#F59E0B" if agent_info['status'] != "Idle" else "#6B7280"
    
    container.markdown(f"""
    <div style="background: white; padding: 1rem; border-radius: 8px; border-left: 4px solid {agent_info['color']}; margin: 0.5rem 0;">
        <h5 style="margin: 0; color: {agent_info['color']};">{agent_name}</h5>
        <p style="margin: 0.5rem 0; color: {status_color}; font-weight: 500;">{agent_info['status']}</p>
        <div style="background: #E5E7EB; border-radius: 4px; height: 8px;">
            <div style="background: {agent_info['color']}; height: 8px; border-radius: 4px; width: {agent_info['progress']}%;"></div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def generate_demo_steps(scenario):
    """Generate demo steps based on scenario"""
    
    base_steps = [
        {"agent": "Infrastructure Architect", "status": "Analyzing requirements", "progress": 20, "description": "Analyzing application requirements"},
        {"agent": "Infrastructure Architect", "status": "Designing architecture", "progress": 60, "description": "Designing optimal architecture"},
        {"agent": "Infrastructure Architect", "status": "Complete", "progress": 100, "description": "Architecture design completed", 
         "output": """apiVersion: v1
kind: Service
metadata:
  name: web-service
spec:
  selector:
    app: web
  ports:
  - port: 80
    targetPort: 8080
  type: LoadBalancer"""},
        
        {"agent": "Security Compliance", "status": "Reviewing policies", "progress": 30, "description": "Reviewing security policies"},
        {"agent": "Security Compliance", "status": "Implementing security", "progress": 80, "description": "Implementing security measures"},
        {"agent": "Security Compliance", "status": "Complete", "progress": 100, "description": "Security implementation completed",
         "output": """{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {"Service": "ec2.amazonaws.com"},
      "Action": "sts:AssumeRole"
    }
  ]
}"""},
        
        {"agent": "Deployment Orchestrator", "status": "Creating pipelines", "progress": 40, "description": "Creating deployment pipelines"},
        {"agent": "Deployment Orchestrator", "status": "Configuring deployment", "progress": 85, "description": "Configuring deployment strategy"},
        {"agent": "Deployment Orchestrator", "status": "Complete", "progress": 100, "description": "Deployment configuration completed",
         "output": """#!/bin/bash
docker build -t myapp:latest .
docker push registry.example.com/myapp:latest
kubectl apply -f deployment.yaml
kubectl rollout status deployment/myapp"""},
        
        {"agent": "Quality Assurance", "status": "Setting up tests", "progress": 25, "description": "Setting up quality tests"},
        {"agent": "Quality Assurance", "status": "Running validations", "progress": 75, "description": "Running comprehensive validations"},
        {"agent": "Quality Assurance", "status": "Complete", "progress": 100, "description": "Quality validation completed"},
        
        {"agent": "Monitoring & Alerting", "status": "Configuring monitoring", "progress": 50, "description": "Configuring monitoring systems"},
        {"agent": "Monitoring & Alerting", "status": "Complete", "progress": 100, "description": "Monitoring setup completed",
         "output": """apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
    - job_name: 'kubernetes-pods'
      kubernetes_sd_configs:
      - role: pod"""}
    ]
    
    return base_steps

def show_performance_comparison():
    """Show performance comparison charts"""
    
    # Performance metrics comparison
    metrics_data = pd.DataFrame({
        'Metric': ['Deployment Speed', 'Code Quality', 'Security Score', 'Error Rate', 'Time to Market'],
        'Traditional DevOps': [30, 65, 60, 25, 40],
        'Single LLM': [60, 75, 70, 15, 65],
        'Our Multi-Agent Platform': [95, 98, 95, 3, 90]
    })
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatterpolar(
        r=metrics_data['Traditional DevOps'],
        theta=metrics_data['Metric'],
        fill='toself',
        name='Traditional DevOps',
        line_color='#DC2626'
    ))
    
    fig.add_trace(go.Scatterpolar(
        r=metrics_data['Single LLM'],
        theta=metrics_data['Metric'],
        fill='toself',
        name='Single LLM',
        line_color='#F59E0B'
    ))
    
    fig.add_trace(go.Scatterpolar(
        r=metrics_data['Our Multi-Agent Platform'],
        theta=metrics_data['Metric'],
        fill='toself',
        name='Our Multi-Agent Platform',
        line_color='#0F766E'
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100]
            )),
        showlegend=True,
        title="Performance Comparison Across Key Metrics",
        height=500
    )
    
    st.plotly_chart(fig, use_container_width=True)

def show_code_generation_demo():
    """Show live code generation capabilities"""
    
    st.markdown("### 💻 Generate Infrastructure Code")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 📝 Requirements")
        app_type = st.selectbox("Application Type", ["Web Application", "API Service", "Data Pipeline", "ML Model"])
        cloud_provider = st.selectbox("Cloud Provider", ["AWS", "Azure", "GCP", "Multi-Cloud"])
        scaling_type = st.selectbox("Scaling Requirements", ["Auto-scaling", "Fixed", "Serverless"])
        
        if st.button("🚀 Generate Code", type="primary"):
            with st.spinner("Agents collaborating to generate code..."):
                time.sleep(2)  # Simulate generation time
                st.session_state.generated_code = generate_sample_code(app_type, cloud_provider, scaling_type)
    
    with col2:
        st.markdown("#### 📄 Generated Code")
        if 'generated_code' in st.session_state:
            st.code(st.session_state.generated_code, language='yaml')
        else:
            st.info("Click 'Generate Code' to see AI-generated infrastructure code")

def generate_sample_code(app_type, cloud_provider, scaling_type):
    """Generate sample infrastructure code"""
    
    return f"""# {app_type} Infrastructure - {cloud_provider}
# Generated by Multi-Agent DevOps Platform

apiVersion: apps/v1
kind: Deployment
metadata:
  name: {app_type.lower().replace(' ', '-')}
  labels:
    app: {app_type.lower().replace(' ', '-')}
    cloud: {cloud_provider.lower()}
spec:
  replicas: {'1' if scaling_type == 'Fixed' else '3'}
  selector:
    matchLabels:
      app: {app_type.lower().replace(' ', '-')}
  template:
    metadata:
      labels:
        app: {app_type.lower().replace(' ', '-')}
    spec:
      containers:
      - name: app
        image: nginx:latest
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "250m"
          limits:
            memory: "128Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: {app_type.lower().replace(' ', '-')}-service
spec:
  selector:
    app: {app_type.lower().replace(' ', '-')}
  ports:
  - port: 80
    targetPort: 80
  type: LoadBalancer"""

def show_quality_dashboard():
    """Show real-time quality metrics dashboard"""
    
    # Generate sample metrics
    current_time = pd.Timestamp.now()
    time_range = pd.date_range(start=current_time - pd.Timedelta(hours=24), end=current_time, freq='h')
    
    metrics_data = pd.DataFrame({
        'timestamp': time_range,
        'quality_score': [95 + random.uniform(-5, 5) for _ in time_range],
        'deployment_success': [98 + random.uniform(-3, 2) for _ in time_range],
        'security_score': [97 + random.uniform(-2, 3) for _ in time_range],
        'performance_score': [94 + random.uniform(-4, 6) for _ in time_range]
    })
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Quality score over time
        fig = px.line(metrics_data, x='timestamp', y='quality_score', 
                     title='Quality Score (24h)', 
                     labels={'quality_score': 'Quality Score (%)', 'timestamp': 'Time'})
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
        
        # Security score gauge
        fig = go.Figure(go.Indicator(
            mode = "gauge+number",
            value = 97.5,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Security Score"},
            gauge = {'axis': {'range': [None, 100]},
                    'bar': {'color': "#0F766E"},
                    'steps': [
                        {'range': [0, 50], 'color': "#FEE2E2"},
                        {'range': [50, 80], 'color': "#FEF3C7"},
                        {'range': [80, 100], 'color': "#D1FAE5"}],
                    'threshold': {'line': {'color': "red", 'width': 4},
                                'thickness': 0.75, 'value': 90}}))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Deployment success rate
        fig = px.line(metrics_data, x='timestamp', y='deployment_success',
                     title='Deployment Success Rate (24h)',
                     labels={'deployment_success': 'Success Rate (%)', 'timestamp': 'Time'})
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
        
        # Performance metrics
        current_metrics = {
            'Avg Response Time': '45ms',
            'Uptime': '99.97%',
            'Error Rate': '0.03%',
            'Throughput': '1.2K req/s'
        }
        
        for metric, value in current_metrics.items():
            st.metric(metric, value)
